# 从issue中提取实体
import json
import os
import pickle
import re

import litellm
import networkx as nx
from datasets import load_dataset
from tqdm import tqdm

# from dependency_graph import RepoEntitySearcher, RepoSearcher
# from dependency_graph.build_graph import (
#     NODE_TYPE_CLASS,
#     NODE_TYPE_FUNCTION,
# )
# from plugins.location_tools.repo_ops.repo_ops import (
#     find_matching_files_from_list,
#     # get_current_issue_data,
#     get_current_repo_modules,
#     get_module_name_by_line_num,
#     reset_current_issue,
#     search_entity_in_global_dict,
#     set_current_issue,
# )
# from util.process_output import parse_keyword_json_obj
# from util.utils import convert_to_json, load_jsonl


PR_TEMPLATE = """
Given the following GitHub problem description, classify the problem statement into the following categories: Problem description, error trace, code to reproduce the bug, and additional context.
--- BEGIN PROBLEM STATEMENT ---

{problem_statement}

--- END PROBLEM STATEMENT ---
"""

EXTRACT_TASK = """Then identify all the potential modules in the '{repo_name}' package mentioned by each category.
Your output should consist of a list of JSON objects, each containing:  
- **keyword**: The class name or function name mentioned.
- **possible_file_path**: A possible file path in the repository where the module might be located.  
- **possible_line_numbers**: An array of line numbers where the module is likely relevant (if applicable or identifiable).  

Example output:  
```json
[
    {{"keyword": "func_1", "possible_file_path": "path/to/file.py", "possible_line_numbers": [10, 25]}},
    {{"keyword": "class_A", "possible_file_path": "path/to/file2.py", "possible_line_numbers": []}},
    {{"keyword": "class_B.func_2", "possible_file_path": "path/to/file3.py", "possible_line_numbers": []}}
]
```
"""


def parse_keyword_json_obj(identified_str):
    # extract json from str contains ```json\n``` use regx
    pattern = r"```json\n(.*?)\n```"
    match = re.search(pattern, identified_str, re.DOTALL)
    if match:
        json_str = match.group(1)
        try:
            json_obj = json.loads(json_str)
        except:
            return None
        return json_obj
    return None


def extract_keywords(problem_statement, repo_name):
    messages = [
        {
            "role": "system",
            "content": "You are a helpful assistant that can interact with a computer to solve tasks.\n<IMPORTANT>\n* If user provides a path, you should NOT assume it's relative to the current working directory. Instead, you should explore the file system to find the file before working on it.\n</IMPORTANT>\n",
        },
        {
            "role": "system",
            "content": PR_TEMPLATE.format(problem_statement=problem_statement),
        },
    ]
    response = litellm.completion(
        model="deepseek/deepseek-chat", messages=messages
    )
    messages.append(response.choices[0].message)
    messages.append(
        {"role": "user", "content": EXTRACT_TASK.format(repo_name=repo_name)}
    )
    response = litellm.completion(
        model="deepseek/deepseek-chat", messages=messages
    )
    messages.append(response.choices[0].message)
    identified_str = response.choices[0].message.content
    parsed_search_terms = parse_keyword_json_obj(identified_str)
    return parsed_search_terms, messages


if __name__ == "__main__":
    import os

    os.environ["DEEPSEEK_API_KEY"] = "***********************************"
    model_name = "deepseek/deepseek-chat"
    output_folder = f"outputs_data/{model_name}/extract_keywords"
    output_file = "extract_keywords_outputs.jsonl"
    output_file = os.path.join(output_folder, output_file)

    dataset = load_dataset("czlll/Loc-Bench_V1", split="test")
    processed_instance = []

    swe_bench_data = dataset.select(range(0, 50))
    for instance in tqdm(swe_bench_data):
        if instance["instance_id"] in processed_instance:
            continue
        search_terms, msg_history = extract_keywords(
            instance["problem_statement"], instance["instance_id"].split("_")[0]
        )
        extracted_data = {
            "instance_id": instance["instance_id"],
            "search_terms": search_terms,
            # 'file_changes': gt_data_dict[instance['instance_id']]['file_changes'],
            "problem_statement": instance["problem_statement"],
            "patch": instance["patch"],
            "repo": instance["repo"],
            "base_commit": instance["base_commit"],
            "messages": msg_history,
        }
