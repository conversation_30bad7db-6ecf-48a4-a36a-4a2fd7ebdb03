import os
from openai import OpenAI

def get_openai_client():
    """
    获取OpenAI客户端，优先使用model_factory配置
    如果model_factory未初始化，则回退到环境变量配置（向后兼容）
    """
    try:
        # 尝试使用model_factory获取LLM配置
        from model_factory import get_model_factory
        model_factory = get_model_factory()
        llm_config = model_factory.get_llm_config()

        client = OpenAI(
            base_url=llm_config.api_base,
            api_key=llm_config.api_key,
        )
        return client
    except (ImportError, RuntimeError):
        # 如果model_factory未初始化，回退到环境变量配置（向后兼容）
        base_url = os.environ.get("OPENAI_BASE_URL") or "https://api.siliconflow.cn/v1"
        api_key = os.environ.get("OPENAI_API_KEY") or "sk-ravccrfhdceccblziftcgyzodzjbftdzrozbofbxrhayptnd"

        client = OpenAI(
            base_url=base_url,
            api_key=api_key,
        )
        return client
