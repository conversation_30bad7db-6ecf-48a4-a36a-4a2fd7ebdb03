import logging
import os
from typing import List, Optional

from llama_index.core import SimpleDirectoryReader
from llama_index.core.schema import Document, TextNode
# import pdb

# 配置日志
logger = logging.getLogger(__name__)

# 如果没有配置根日志器，则进行基本配置
if not logging.getLogger().handlers:
    logging.basicConfig(
        level=logging.INFO,
        format="%(asctime)s - %(filename)s:%(lineno)s - %(levelname)s - %(message)s",
        datefmt="%Y-%m-%d %H:%M:%S",
    )


class BaseIndexer:
    def __init__(self, persist_path: Optional[str] = None, **kwargs):
        self.persist_path = persist_path
        self.kwargs = kwargs
        self.index = None
        self.repo_path = None

    def load_documents(
        self, repo_path: str, file_extns: List[str] = [".py", ".ts", ".ets", ".js"]
    ) -> List[Document]:
        self.repo_path = repo_path
        
        """
        从代码仓库加载文档

        Args:
            repo_path: 代码仓库路径
            file_extns: 要加载的文件扩展名列表，例如 ['.py', '.js']

        Returns:
            加载的文档列表
        """
        logger.info(f"开始从仓库加载文档: {repo_path}")
        self.repo_path = repo_path

        if file_extns is None:
            file_extns = [".py", ".ets"]
            logger.debug(f"使用默认文件扩展名: {file_extns}")
        else:
            logger.debug(f"使用自定义文件扩展名: {file_extns}")

        # 使用 SimpleDirectoryReader 加载文档
        try:
            reader = SimpleDirectoryReader(
                input_dir=repo_path,
                recursive=True,
                required_exts=file_extns,
                filename_as_id=True,
            )
            documents = reader.load_data()
            for i in range(len(documents)):
                documents[i].metadata["file_path"] = documents[i].metadata["file_path"].replace(repo_path, "").lstrip(os.path.sep)
            logger.info(f"成功加载了 {len(documents)} 个文档")
        except Exception as e:
            logger.error(f"加载文档时出错: {str(e)}")
            raise

        return documents

    def process_documents(self, documents: list) -> List[TextNode]:
        pass

    def build_index(self, nodes: List[TextNode]):
        pass

    def load_index(self):
        pass

    def retrieve(self, query: str, top_k: int = 5) -> List[TextNode]:
        pass
