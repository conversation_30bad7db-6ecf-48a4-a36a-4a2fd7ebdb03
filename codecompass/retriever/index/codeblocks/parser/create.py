from typing import Optional

from .parser import CodeParser
from .python import Python<PERSON>arser
from .typescript import TypescriptParser


def is_supported(language: str) -> bool:
    return language in ["python", "java", "typescript", "javascript"]


def create_parser(language: str, **kwargs) -> Optional[CodeParser]:
    if language == "python":
        return PythonParser(**kwargs)
    elif language == "typescript":
        return TypescriptParser(**kwargs)

    raise NotImplementedError(f"Language {language} is not supported.")
