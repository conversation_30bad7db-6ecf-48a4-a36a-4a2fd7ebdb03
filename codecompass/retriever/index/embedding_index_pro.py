import sys
from pathlib import Path

sys.path.append(str(Path(__file__).parent.parent.parent.parent))
import logging
import os
import hashlib
import pickle
from typing import List, Optional, Dict, Set, Tuple

from llama_index.core.indices.vector_store.base import VectorStoreIndex
from llama_index.core.retrievers import VectorIndexRetriever
from llama_index.core.retrievers import (
    VectorIndexRetriever as EmbeddingRetriever,
)

from llama_index.core.node_parser import CodeSplitter, SentenceSplitter
from llama_index.core.schema import Document, TextNode
from llama_index.embeddings.openai import OpenAIEmbedding
from pydantic import BaseModel
from tqdm import tqdm

from codecompass.retriever.index.utils.epic_split import EpicSplitter
from codecompass.retriever.index.utils.file_tracker import create_file_tracker
from codecompass.retriever.index.repo_ops import BaseIndexer, logger

# 模型工厂，统一管理模型创建
def get_embedding_model():
    """获取embedding模型，优先使用模型工厂"""
    try:
        from model_factory import get_model_factory
        return get_model_factory().get_embedding_model()
    except RuntimeError:
        # 如果模型工厂未初始化，回退到环境变量配置（向后兼容）
        import os
        api_key = os.environ.get("EMBEDDING_API_KEY") or ""
        if not api_key:
            raise ValueError("Embedding API密钥未配置。请设置 EMBEDDING_API_KEY 环境变量")
        
        api_base = os.environ.get("EMBEDDING_API_BASE") or ""
        if not api_base:
            raise ValueError("Embedding API基础URL未配置。请设置 EMBEDDING_API_BASE 环境变量")
            
        model_name = os.environ.get("EMBEDDING_MODEL_NAME") or ""
        if not model_name:
            raise ValueError("Embedding模型名称未配置。请设置 EMBEDDING_MODEL_NAME 环境变量")
        
        return OpenAIEmbedding(
            model_name=model_name,
            api_base=api_base,
            api_key=api_key,
            embed_batch_size=10,
        )


def generate_node_id(node: TextNode) -> str:
    """
    为节点生成唯一标识符
    基于文件路径、起始行、结束行和内容hash
    """
    file_path = node.metadata.get("file_path", "")
    start_line = node.metadata.get("start_line", 0)
    end_line = node.metadata.get("end_line", 0)
    content_hash = hashlib.md5(node.text.encode('utf-8')).hexdigest()[:8]
    
    return f"{file_path}:{start_line}-{end_line}:{content_hash}"


class ChunkConfig(BaseModel):
    """
    用于配置代码切片的参数
    """

    chunk_lines: int = 100
    chunk_lines_overlap: int = 15
    max_chars: int = 1500


class EpicChunkConfig(BaseModel):
    min_chunk_size: int = 100
    chunk_size: int = 500
    max_chunk_size: int = 2000
    hard_token_limit: int = 2000
    max_chunks: int = 200


def process_node(doc: Document, doc_node: TextNode):
    return doc_node


def add_line_numbers_to_sentence_nodes(doc: Document, nodes: List[TextNode]) -> List[TextNode]:
    """
    为SentenceSplitter生成的节点添加start_line和end_line信息
    
    Args:
        doc: 原始文档
        nodes: SentenceSplitter生成的节点列表
    
    Returns:
        添加了行号信息的节点列表
    """
    if not nodes:
        return nodes
    
    # 获取文档的全部内容和按行分割
    full_content = doc.text
    
    # 为每个节点计算行号
    line_set = set()
    for node in nodes:
        if not node.text.strip():
            continue
        
        if node.text in full_content:
            start_char_index = full_content.index(node.text)
            start_line = full_content[:start_char_index].count('\n') + 1
            end_line = start_line + node.text.count('\n')
            while (start_line,end_line) in line_set:
                try: start_char_index = full_content.index(node.text, start_char_index + 1)
                except: continue
                start_line = full_content[:start_char_index].count('\n') + 1
                end_line = start_line + node.text.count('\n')
            line_set.add((start_line,end_line))
            # 添加行号信息到节点元数据
            node.metadata["start_line"] = start_line
            node.metadata["end_line"] = end_line
    
    return nodes


class EmbeddingIndexPro(BaseIndexer):
    """
    使用Embedding策略对代码进行切片和索引的简单实现
    """

    def __init__(self, persist_path: Optional[str] = None, **kwargs):
        """
        初始化Embedding索引

        Args:
            persist_path: 索引持久化路径，如果提供，将从该路径加载或保存索引
        """
        self.persist_path = persist_path
        self._retriever = None
        self.split_config: ChunkConfig = ChunkConfig(**kwargs)
        self.epic_split_config: EpicChunkConfig = EpicChunkConfig(**kwargs)
        self.embedding_config = None
        self._embedding_model = None
        
        # 增量构建相关属性
        self._previous_nodes: Dict[str, TextNode] = {}  # 保存之前的节点状态
        self._nodes_state_file = None  # 节点状态文件路径
        self._file_tracker = None  # 文件跟踪器
        
        if self.persist_path:
            self._nodes_state_file = os.path.join(
                os.path.dirname(self.persist_path), 
                "embedding_nodes_state.pkl"
            )
            # 初始化文件跟踪器
            persist_dir = os.path.dirname(self.persist_path)
            self._file_tracker = create_file_tracker("", persist_dir, "embedding_file_tracker")

        logger.info(
            f"初始化 EmbeddingIndexPro，持久化路径: {persist_path if persist_path else '未设置'}"
        )
        logger.debug(f"切片配置: {self.split_config}")

    def _save_nodes_state(self, nodes: List[TextNode]) -> None:
        """保存节点状态到文件"""
        if not self._nodes_state_file:
            return
            
        # 将节点转换为可序列化的格式
        nodes_dict = {}
        for node in nodes:
            node_id = generate_node_id(node)
            # 确保node有唯一的node_id
            if not hasattr(node, 'node_id') or not node.node_id:
                import uuid
                node.node_id = str(uuid.uuid4())
            
            nodes_dict[node_id] = {
                'text': node.text,
                'metadata': node.metadata,
                'node_id': node.node_id,
                'start_char_idx': getattr(node, 'start_char_idx', None),
                'end_char_idx': getattr(node, 'end_char_idx', None),
            }
        
        os.makedirs(os.path.dirname(self._nodes_state_file), exist_ok=True)
        with open(self._nodes_state_file, 'wb') as f:
            pickle.dump(nodes_dict, f)
        
        logger.debug(f"节点状态已保存到 {self._nodes_state_file}")

    def _load_nodes_state(self) -> Dict[str, TextNode]:
        """从文件加载节点状态"""
        if not self._nodes_state_file or not os.path.exists(self._nodes_state_file):
            return {}
        
        try:
            with open(self._nodes_state_file, 'rb') as f:
                nodes_dict = pickle.load(f)
            
            # 将字典转换回TextNode对象
            previous_nodes = {}
            for node_id, node_data in nodes_dict.items():
                node = TextNode(
                    text=node_data['text'],
                    metadata=node_data['metadata']
                )
                # 恢复节点的所有属性
                if node_data.get('node_id'):
                    node.node_id = node_data['node_id']
                if node_data.get('start_char_idx') is not None:
                    node.start_char_idx = node_data['start_char_idx']
                if node_data.get('end_char_idx') is not None:
                    node.end_char_idx = node_data['end_char_idx']
                    
                previous_nodes[node_id] = node
            
            logger.debug(f"从 {self._nodes_state_file} 加载了 {len(previous_nodes)} 个节点状态")
            return previous_nodes
            
        except Exception as e:
            logger.warning(f"加载节点状态失败: {str(e)}")
            return {}

    def _compute_nodes_diff(self, new_nodes: List[TextNode]) -> Tuple[List[TextNode], List[str], List[TextNode]]:
        """
        计算新节点与之前节点的差异
        
        Returns:
            Tuple[新增节点列表, 删除节点ID列表, 未变化节点列表]
        """
        # 加载之前的节点状态
        previous_nodes = self._load_nodes_state()
        
        # 为新节点生成ID映射
        new_nodes_dict = {}
        for node in new_nodes:
            node_id = generate_node_id(node)
            new_nodes_dict[node_id] = node
        
        # 计算差异
        new_node_ids = set(new_nodes_dict.keys())
        previous_node_ids = set(previous_nodes.keys())
        
        # 新增的节点
        added_node_ids = new_node_ids - previous_node_ids
        added_nodes = [new_nodes_dict[node_id] for node_id in added_node_ids]
        
        # 删除的节点
        deleted_node_ids = list(previous_node_ids - new_node_ids)
        
        # 未变化的节点
        unchanged_node_ids = new_node_ids & previous_node_ids
        unchanged_nodes = [new_nodes_dict[node_id] for node_id in unchanged_node_ids]
        
        logger.info(f"节点差异分析: 新增 {len(added_nodes)} 个, 删除 {len(deleted_node_ids)} 个, 未变化 {len(unchanged_nodes)} 个")
        
        return added_nodes, deleted_node_ids, unchanged_nodes

    def get_embedding_model(self):
        """获取embedding模型实例"""
        if self._embedding_model is None:
            if self.embedding_config:
                self._embedding_model = OpenAIEmbedding(
                    model_name=self.embedding_config.model_name,
                    api_base=self.embedding_config.api_base,
                    api_key=self.embedding_config.api_key,
                    embed_batch_size=self.embedding_config.embed_batch_size,
                )
            else:
                # 使用统一的模型工厂获取embedding模型
                self._embedding_model = get_embedding_model()
        return self._embedding_model

    def process_documents(self, documents: list, incremental: bool = False, file_extensions: List[str] = [".py", ".ts", ".ets", ".js"]) -> List[TextNode]:
        """
        处理文档，将代码切片成节点，支持增量处理

        Args:
            documents: 文档列表
            incremental: 是否启用增量处理
            file_extensions: 支持的文件扩展名

        Returns:
            处理后的节点列表
        """
        logger.info(f"开始处理文档并生成节点 (增量模式: {incremental})")

        if not documents:
            logger.error("没有可处理的文档，请先加载文档")
            raise ValueError("请先加载文档")

        # 如果不是增量模式，使用原有的处理方式
        if not incremental or not self._file_tracker or not self.repo_path:
            return self._process_all_documents(documents)

        # 增量处理模式 - 只返回新处理的节点，在build_index中进行节点合并
        new_nodes, changed_files, deleted_files = self._process_documents_incremental(documents, file_extensions)
        
        # 将变化信息存储到实例变量中，供build_index使用
        self._incremental_info = {
            'new_nodes': new_nodes,
            'changed_files': changed_files,
            'deleted_files': deleted_files
        }
        
        return new_nodes
    
    def _process_all_documents(self, documents: list) -> List[TextNode]:
        """处理所有文档（非增量模式）"""
        logger.info("使用全量处理模式")
        
        splitter = EpicSplitter(
            **self.epic_split_config.model_dump(),
            repo_path=self.repo_path,
        )
        temp_splitter = SentenceSplitter(
            chunk_size=self.split_config.chunk_lines * 16,
            chunk_overlap=self.split_config.chunk_lines_overlap * 16,
            paragraph_separator="\n\n",
        )

        nodes = []
        for doc in tqdm(documents, miniters=10, desc="处理文档:"):
            file_path = doc.metadata.get("file_path", "")
            language = Path(file_path).suffix.lstrip(".")
            logger.debug(f"处理文件: {file_path}, 扩展名: {language}")

            if language in ["py", "python"]:
                language = "python"
            elif language in ["ets", "arkts", "ts", "typescript"]:
                language = "typescript"
            else:
                continue

            splitter.language = language
            logger.debug(f"设置切片器语言为: {language}")

            try:
                doc_nodes = splitter.get_nodes_from_documents([doc])
                doc_nodes = [process_node(doc, node) for node in doc_nodes]
                if len(doc_nodes) == 0:
                    doc_nodes = temp_splitter.get_nodes_from_documents([doc])
                    doc_nodes = add_line_numbers_to_sentence_nodes(doc, doc_nodes)
                
                logger.debug(f"文件 {file_path}: 生成 {len(doc_nodes)} 个节点")
                nodes.extend(doc_nodes)
                
            except Exception as e:
                logger.warning(f"处理文件 {file_path} 时出错: {str(e)}，跳过此文件")
                continue

        logger.info(f"文档处理完成，共生成 {len(nodes)} 个代码节点")
        return nodes
    
    def _process_documents_incremental(self, documents: list, file_extensions: List[str]) -> Tuple[List[TextNode], List[str], List[str]]:
        """
        增量处理文档
        
        Returns:
            Tuple[新处理的节点, 修改的文件列表, 删除的文件列表]
        """
        logger.info("使用增量处理模式")
        
        # 设置文件跟踪器的仓库路径
        self._file_tracker.repo_path = self.repo_path
        
        # 检测文件变化
        added_files, deleted_files, modified_files = self._file_tracker.detect_file_changes(file_extensions)
        
        # 为文档创建路径映射
        doc_by_path = {doc.metadata.get("file_path", ""): doc for doc in documents}
        
        # 只处理新增和修改的文件
        files_to_process = added_files + modified_files
        new_nodes = []
        
        if files_to_process:
            logger.info(f"需要处理的文件: 新增 {len(added_files)} 个, 修改 {len(modified_files)} 个")
            
            splitter = EpicSplitter(
                **self.epic_split_config.model_dump(),
                repo_path=self.repo_path,
            )
            temp_splitter = SentenceSplitter(
                chunk_size=self.split_config.chunk_lines * 16,
                chunk_overlap=self.split_config.chunk_lines_overlap * 16,
                paragraph_separator="\n\n",
            )
            
            processed_count = 0
            for file_path in tqdm(files_to_process, desc="处理变化文件"):
                if file_path not in doc_by_path:
                    logger.warning(f"文件 {file_path} 在文档列表中未找到，跳过")
                    continue
                
                doc = doc_by_path[file_path]
                language = Path(file_path).suffix.lstrip(".")
                
                if language in ["py", "python"]:
                    language = "python"
                elif language in ["ets", "arkts", "ts", "typescript"]:
                    language = "typescript"
                else:
                    continue
                
                splitter.language = language
                
                try:
                    doc_nodes = splitter.get_nodes_from_documents([doc])
                    doc_nodes = [process_node(doc, node) for node in doc_nodes]
                    if len(doc_nodes) == 0:
                        doc_nodes = temp_splitter.get_nodes_from_documents([doc])
                        doc_nodes = add_line_numbers_to_sentence_nodes(doc, doc_nodes)
                    
                    logger.debug(f"文件 {file_path}: 生成 {len(doc_nodes)} 个节点")
                    new_nodes.extend(doc_nodes)
                    processed_count += 1
                        
                except Exception as e:
                    logger.warning(f"处理文件 {file_path} 时出错: {str(e)}，跳过此文件")
                    continue
        
        logger.info(f"增量处理完成: 处理了 {len(new_nodes)} 个新节点")
        
        # 记录被删除文件的信息
        if deleted_files:
            logger.info(f"检测到 {len(deleted_files)} 个文件被删除")
            for file_path in deleted_files:
                logger.debug(f"删除的文件: {file_path}")
        
        # 返回新节点和变化信息
        changed_files = modified_files + deleted_files
        return new_nodes, changed_files, deleted_files
    
    def update_file_tracker_state(self, file_extensions: List[str]) -> None:
        """更新文件跟踪器状态（用于全量重建后同步状态）"""
        if self._file_tracker and self.repo_path:
            self._file_tracker.repo_path = self.repo_path
            self._file_tracker.force_update_state(file_extensions)
            logger.info("文件跟踪器状态已更新")

    def build_index(self, nodes: List[TextNode], incremental: bool = False) -> EmbeddingRetriever:
        """
        构建 Embedding 索引，支持真正的增量更新

        Args:
            nodes: 节点列表
            incremental: 是否为增量更新模式

        Returns:
            EmbeddingRetriever 实例
        """
        logger.info(f"开始构建 Embedding 索引 (增量模式: {incremental})")

        if not incremental and not nodes: # 增量模式仅删除可能没有节点
            logger.error("没有可索引的节点，请先处理文档")
            raise ValueError("请先处理文档")

        if incremental and self.persist_path:
            # 检查是否有增量信息
            if hasattr(self, '_incremental_info'):
                # 增量更新模式 - 使用文件级别的变化信息
                incremental_info = self._incremental_info
                new_nodes = incremental_info['new_nodes']
                changed_files = incremental_info['changed_files']
                deleted_files = incremental_info['deleted_files']
                
                if not new_nodes and not changed_files and not deleted_files:
                    logger.info("没有检测到文件变化，跳过索引更新")
                    if self._retriever is None:
                        # 如果当前没有retriever，尝试加载现有索引
                        try:
                            self.load_index()
                        except Exception as e:
                            logger.warning(f"加载现有索引失败: {str(e)}，将重新构建完整索引")
                            index = VectorStoreIndex(
                                nodes=nodes, 
                                embed_model=self.get_embedding_model(), 
                                show_progress=True
                            )
                            self._retriever = index.as_retriever()
                            # 保存索引和状态
                            if self.persist_path:
                                os.makedirs(os.path.dirname(self.persist_path), exist_ok=True)
                                index.storage_context.persist(persist_dir=self.persist_path)
                                self._save_nodes_state(nodes)
                    return self._retriever
                
                # 加载之前的节点状态
                previous_nodes = self._load_nodes_state()
                
                # 1. 找到需要删除的旧节点
                old_nodes_to_remove = []
                old_node_ids_to_delete = []
                for file_path in changed_files:
                    old_file_nodes = [node for node_id, node in previous_nodes.items() 
                                     if node.metadata.get("file_path") == file_path]
                    old_nodes_to_remove.extend(old_file_nodes)
                    # 收集实际的node_id用于删除操作
                    for node in old_file_nodes:
                        if hasattr(node, 'node_id') and node.node_id:
                            old_node_ids_to_delete.append(node.node_id)
                
                # 2. 保留未变化文件的节点
                unchanged_nodes = []
                all_changed_files = set(changed_files)
                for node_id, node in previous_nodes.items():
                    file_path = node.metadata.get("file_path", "")
                    if file_path not in all_changed_files:
                        unchanged_nodes.append(node)
                
                # 3. 合并最终节点列表
                final_nodes = unchanged_nodes + new_nodes
                
                logger.info(f"Embedding增量更新: 删除 {len(old_nodes_to_remove)} 个旧节点, "
                           f"保留 {len(unchanged_nodes)} 个未变化节点, "
                           f"新增 {len(new_nodes)} 个节点, 总计 {len(final_nodes)} 个节点")
                
                # 尝试加载现有索引进行增量更新
                try:
                    logger.info("加载现有索引进行增量更新")
                    from llama_index.core import StorageContext, load_index_from_storage
                    
                    # 获取embedding模型配置
                    embed_model = self.get_embedding_model()
                    
                    # 临时设置环境变量，确保load_index_from_storage可以正常工作
                    original_api_key = os.environ.get("OPENAI_API_KEY")
                    original_api_base = os.environ.get("OPENAI_API_BASE")
                    
                    try:
                        # 设置临时环境变量
                        os.environ["OPENAI_API_KEY"] = embed_model.api_key
                        if hasattr(embed_model, 'api_base') and embed_model.api_base:
                            os.environ["OPENAI_API_BASE"] = embed_model.api_base
                        
                        storage_context = StorageContext.from_defaults(
                            persist_dir=self.persist_path
                        )
                        index = load_index_from_storage(storage_context, embed_model=embed_model)
                        
                    finally:
                        # 恢复原始环境变量
                        if original_api_key is not None:
                            os.environ["OPENAI_API_KEY"] = original_api_key
                        else:
                            os.environ.pop("OPENAI_API_KEY", None)
                        
                        if original_api_base is not None:
                            os.environ["OPENAI_API_BASE"] = original_api_base
                        else:
                            os.environ.pop("OPENAI_API_BASE", None)
                    
                    # 执行真正的增量操作
                    if old_node_ids_to_delete:
                        logger.info(f"删除 {len(old_node_ids_to_delete)} 个过时节点")
                        index.delete_nodes(old_node_ids_to_delete, delete_from_docstore=True)
                        logger.info(f"成功删除 {len(old_node_ids_to_delete)} 个节点")
                    
                    if new_nodes:
                        logger.info(f"插入 {len(new_nodes)} 个新节点")
                        index.insert_nodes(new_nodes)
                        logger.info(f"成功插入 {len(new_nodes)} 个节点")
                    
                    # 更新retriever
                    self._retriever = index.as_retriever()
                    
                    # 保存更新后的索引和节点状态
                    index.storage_context.persist(persist_dir=self.persist_path)
                    self._save_nodes_state(final_nodes)
                    logger.info(f"增量更新的索引已保存到 {self.persist_path}")
                    
                except Exception as e:
                    logger.warning(f"增量更新失败: {str(e)}，回退到完整重建")
                    # 回退到完整重建
                    logger.debug(f"完整重建Embedding索引，使用 {len(final_nodes)} 个节点")
                    index = VectorStoreIndex(
                        nodes=final_nodes, 
                        embed_model=self.get_embedding_model(), 
                        show_progress=True
                    )
                    self._retriever = index.as_retriever()
                    
                    if self.persist_path:
                        os.makedirs(os.path.dirname(self.persist_path), exist_ok=True)
                        index.storage_context.persist(persist_dir=self.persist_path)
                        self._save_nodes_state(final_nodes)
                        logger.info(f"完整重建的索引已保存到 {self.persist_path}")
            else:
                # 回退到原有的节点差异分析方法
                logger.info("未找到增量信息，使用节点差异分析进行增量更新")
                added_nodes, deleted_node_ids, unchanged_nodes = self._compute_nodes_diff(nodes)
                
                if not added_nodes and not deleted_node_ids:
                    logger.info("没有检测到节点变化，跳过索引更新")
                    if self._retriever is None:
                        try:
                            self.load_index()
                        except Exception as e:
                            logger.warning(f"加载现有索引失败: {str(e)}，将重新构建完整索引")
                            index = VectorStoreIndex(
                                nodes=nodes, 
                                embed_model=self.get_embedding_model(), 
                                show_progress=True
                            )
                            self._retriever = index.as_retriever()
                            if self.persist_path:
                                os.makedirs(os.path.dirname(self.persist_path), exist_ok=True)
                                index.storage_context.persist(persist_dir=self.persist_path)
                                self._save_nodes_state(nodes)
                    return self._retriever
                
                # 使用原有逻辑进行增量更新（重建方式）
                logger.info(f"Embedding增量重建: 使用 {len(unchanged_nodes)} 个未变化节点，新增 {len(added_nodes)} 个节点")
                current_nodes = unchanged_nodes + added_nodes
                
                try:
                    logger.debug(f"重建Embedding索引，使用 {len(current_nodes)} 个节点")
                    index = VectorStoreIndex(
                        nodes=current_nodes, 
                        embed_model=self.get_embedding_model(), 
                        show_progress=True
                    )
                    self._retriever = index.as_retriever()
                    
                    if self.persist_path:
                        os.makedirs(os.path.dirname(self.persist_path), exist_ok=True)
                        index.storage_context.persist(persist_dir=self.persist_path)
                        self._save_nodes_state(nodes)
                        logger.info(f"增量更新的索引已保存到 {self.persist_path}")
                        
                except Exception as e:
                    logger.error(f"增量更新索引时出错: {str(e)}")
                    raise
        else:
            # 完全重建模式
            logger.debug(f"完全重建Embedding索引，使用 {len(nodes)} 个节点")
            index = VectorStoreIndex(
                nodes=nodes, embed_model=self.get_embedding_model(), show_progress=True
            )
            # 创建检索器并保存到本地
            retriever = index.as_retriever()
            # retriever = VectorIndexRetriever(index)
            if self.persist_path:
                os.makedirs(os.path.dirname(self.persist_path), exist_ok=True)
                index.storage_context.persist(persist_dir=self.persist_path)
                self._save_nodes_state(nodes)
                logger.info(f"索引已成功保存到 {self.persist_path}")
            else:
                logger.warning("未提供持久化路径，索引不会被保存")
            self._retriever = retriever

        return self._retriever

    def load_index(self) -> EmbeddingRetriever:
        """
        从持久化路径加载索引

        Returns:
            EmbeddingRetriever 实例
        """
        logger.info(f"尝试从 {self.persist_path} 加载索引")

        if not self.persist_path:
            logger.error("未提供持久化路径，无法加载索引")
            raise ValueError("未提供持久化路径")

        try:
            # load from persit path
            from llama_index.core import StorageContext, load_index_from_storage
            
            # 获取embedding模型配置
            embed_model = self.get_embedding_model()
            
            # 临时设置环境变量，确保load_index_from_storage可以正常工作
            original_api_key = os.environ.get("OPENAI_API_KEY")
            original_api_base = os.environ.get("OPENAI_API_BASE")
            
            try:
                # 设置临时环境变量
                os.environ["OPENAI_API_KEY"] = embed_model.api_key
                if hasattr(embed_model, 'api_base') and embed_model.api_base:
                    os.environ["OPENAI_API_BASE"] = embed_model.api_base
                
                storage_context = StorageContext.from_defaults(
                    persist_dir=self.persist_path
                )
                index = load_index_from_storage(storage_context, embed_model=embed_model)
            finally:
                # 恢复原始环境变量
                if original_api_key is not None:
                    os.environ["OPENAI_API_KEY"] = original_api_key
                else:
                    os.environ.pop("OPENAI_API_KEY", None)
                
                if original_api_base is not None:
                    os.environ["OPENAI_API_BASE"] = original_api_base
                else:
                    os.environ.pop("OPENAI_API_BASE", None)
            
            retriever = VectorIndexRetriever(
                index,
                embed_model=embed_model,
                similarity_top_k=5,
            )
            self._retriever = retriever
            logger.info(f"成功从 {self.persist_path} 加载了索引")
        except Exception as e:
            logger.error(f"加载索引时出错: {str(e)}")
            raise

        return self._retriever

    def retrieve(
        self, query: str, top_k: int = 5, threshold: float = 0.0
    ) -> List[TextNode]:
        """
        检索与查询相关的代码片段

        Args:
            query: 查询字符串
            top_k: 返回的结果数量

        Returns:
            检索到的节点列表
        """
        logger.info(
            f"执行检索查询: '{query}'，top_k={top_k}, threshold={threshold}"
        )

        if not self._retriever:
            logger.error("检索器未初始化，请先构建或加载索引")
            raise ValueError("请先构建或加载索引")

        # 执行检索
        try:
            retrieved_nodes = self._retriever.retrieve(query)
            logger.debug(f"检索到 {len(retrieved_nodes)} 个原始结果")

            # # 如果需要限制结果数量，可以在这里手动截取
            # if len(retrieved_nodes) > top_k:
            #     retrieved_nodes = retrieved_nodes[:top_k]
            #     logger.debug(f"结果已截取为前 {top_k} 个")

            # 排序和过滤
            retrieved_nodes = sorted(
                retrieved_nodes, key=lambda x: x.score, reverse=True
            )
            retrieved_nodes = [
                node for node in retrieved_nodes if node.score >= threshold
            ]
            retrieved_nodes = retrieved_nodes[:top_k]

            retrieved_nodes = [node for node in retrieved_nodes if hasattr(node, "score") and node.score > 0.01]
            for node in retrieved_nodes:
                node.metadata["category"] = "embedding"

            # 记录检索结果的基本信息
            for i, node in enumerate(retrieved_nodes):
                file_path = node.metadata.get("file_path", "Unknown")
                score = node.score if hasattr(node, "score") else "N/A"
                logger.debug(f"结果 {i + 1}: 文件={file_path}, 相关度={score}")

        except Exception as e:
            logger.error(f"执行检索时出错: {str(e)}")
            raise

        return retrieved_nodes


def build_embedding_index(repo_path: str, persist_path: str, file_extensions: list = [".py", ".ts", ".ets", ".js"], embedding_config=None, force: bool = False, incremental: bool = False, **config_kwargs) -> EmbeddingIndexPro:
    """
    构建Embedding索引的主函数
    
    Args:
        repo_path: 代码仓库路径
        persist_path: 索引持久化路径
        file_extensions: 支持的文件扩展名列表
        embedding_config: Embedding配置对象
        force: 是否强制重建索引
        incremental: 是否启用增量构建模式
        **config_kwargs: 其他配置参数
    
    Returns:
        EmbeddingIndexPro实例
    """
    logger.info(f"开始构建Embedding索引: {repo_path} -> {persist_path} (增量模式: {incremental})")
    
    index = EmbeddingIndexPro(persist_path=persist_path, **config_kwargs)
    
    # 设置embedding配置
    if embedding_config:
        index.embedding_config = embedding_config
    
    # 构建索引
    if not force and not incremental and os.path.exists(persist_path):
        logger.info("检测到现有索引，将加载现有索引")
        index.load_index()
    else:
        if incremental:
            logger.info("启用增量构建模式")
        else:
            logger.info("将构建新索引")
        documents = index.load_documents(repo_path=repo_path, file_extns=file_extensions)
        nodes = index.process_documents(documents, incremental=incremental, file_extensions=file_extensions)
        index.build_index(nodes, incremental=incremental)
        
        # 无论是否增量模式，都需要更新文件跟踪器状态以保持同步
        index.update_file_tracker_state(file_extensions)
    
    return index


def main():
    """兼容命令行调用的主函数"""
    import argparse

    parser = argparse.ArgumentParser(
        description="使用 Embedding 策略对代码进行切片和索引的简单实现"
    )
    parser.add_argument(
        "--repo_path",
        type=str,
        required=True,
        help="代码仓库路径",
    )
    parser.add_argument(
        "--persist_path",
        type=str,
        required=True,
        help="索引持久化路径",
    )
    parser.add_argument(
        "--chunk_lines",
        type=int,
        default=100,
        help="切片的行数",
    )
    parser.add_argument(
        "--chunk_lines_overlap",
        type=int,
        default=15,
        help="切片的重叠行数",
    )
    parser.add_argument(
        "--max_chars",
        type=int,
        default=1500,
        help="切片的最大字符数",
    )

    args = parser.parse_args()
    
    config_kwargs = {
        "chunk_lines": args.chunk_lines,
        "chunk_lines_overlap": args.chunk_lines_overlap,
        "max_chars": args.max_chars,
    }
    
    build_embedding_index(args.repo_path, args.persist_path, **config_kwargs)


# 使用示例
def example():
    # 配置日志
    logging.basicConfig(
        level=logging.INFO,
        format="%(asctime)s - %(filename)s - %(levelname)s - %(message)s",
        datefmt="%Y-%m-%d %H:%M:%S",
    )

    logger.info("开始执行 EmbeddingIndexSimple 示例")

    # 初始化 Embedding 索引
    repo_path = "/Users/<USER>/ide_dev/repo/CodebaseQA/example_repos/edge_infer"
    repo_name = os.path.basename(repo_path)
    persist_path = f"/Users/<USER>/ide_dev/repo/CodebaseQA/codecompass/temp/embedding_index_pro/{repo_name}"


    logger.info(f"使用仓库: {repo_path}")
    logger.info(f"索引持久化路径: {persist_path}")

    index = EmbeddingIndexPro(persist_path=persist_path)

    # 构建索引
    if os.path.exists(persist_path):
        logger.info("检测到现有索引，将加载现有索引")
        index.load_index()
    else:
        logger.info("未检测到现有索引，将构建新索引")
        documents = index.load_documents(repo_path=repo_path)
        nodes = index.process_documents(documents)
        index.build_index(nodes)

    # 检索示例
    # query = "查找文件读取相关的代码, 比如hello world"
    # query = "聊天接口"
    query = "TabBarData"
    logger.info(f"执行示例查询: '{query}'")
    results = index.retrieve(query)

    # 打印结果
    logger.info(f"共检索到 {len(results)} 个结果")
    for i, node in enumerate(results):
        start_line = node.metadata.get("start_line", "Unknown")
        end_line = node.metadata.get("end_line", "Unknown")
        logger.info(f"结果 {i + 1}:".center(30, "-"))
        logger.info(
            f"文件: {node.metadata.get('file_path', 'Unknown')}:({start_line}-{end_line})"
        )
        logger.info(
            f"相关度: {node.score if hasattr(node, 'score') else 'N/A'}"
        )
        logger.info(f"内容: {node.text[:200]}...")

    logger.info("示例执行完成")


if __name__ == "__main__":
    example()
    # main()
