import os
import fnmatch
from pathlib import Path

import sys

sys.path.insert(0, str(Path(__file__).parent.parent.parent))
from codecompass.llm.openai_provider import get_openai_client


def get_project_structure(repo_path):
    """Generate a tree-like project structure representation.

    Args:
        repo_path: Root path of the repository

    Returns:
        str: Formatted string showing directory structure
    """
    exclude_pattern = [
        "*.venv*",
        "*.git*",
        "*.vscode*",
        "*.pytest_cache*",
        "*__pycache__*",
        "*.egg-info*",
        "*.pyc",
        "*.cache",
        "*.log",
        "*.log.*",
        "*logs",
        "*.png",
        "*.jpeg",
        "*.jpg",
        "*.json",
        "*.json5",
        "*.sample",
        "*.svg",
    ]

    output = []
    for root, dirs, files in os.walk(repo_path):
        level = root.replace(repo_path, "").count(os.sep)
        indent = "│   " * level
        if any(fnmatch.fnmatch(root, pattern) for pattern in exclude_pattern):
            continue

        # Add directory name
        basename = os.path.basename(root)
        if level == 0:
            output.append(f"{basename}/")
        else:
            output.append(f"{indent[:-4]}├── {basename}/")

        files = [
            f
            for f in files
            if not any(
                fnmatch.fnmatch(f, pattern) for pattern in exclude_pattern
            )
        ]

        # Add files
        for file in sorted(files):
            output.append(f"{indent}├── {file}")

    return "\n".join(output)


def get_repo_global_understand(repo_path):
    client = get_openai_client()
    structure = get_project_structure(repo_path)
    prompt = f"""
    你是一个代码理解助手，我会给你一份项目的目录结构，你需要理解这个项目的目录结构.
    目录结构:
    {structure}
    =====================
    请你对目录结构进行理解，并总结项目的主要功能和架构。
    """
    response = client.chat.completions.create(
        model="Qwen/Qwen3-8B",
        messages=[
            {"role": "user", "content": prompt},
        ],
    )
    return response.choices[0].message.content


if __name__ == "__main__":
    repo_path = "/Users/<USER>/projects/hmosworld"
    # structure = get_project_structure(repo_path)
    # print(structure)
    print(get_repo_global_understand(repo_path))
    repo_name = Path(repo_path).name
    persist_path = f"/Users/<USER>/projects/code_qa/codecompass/temp/understand/{repo_name}.md"
    Path(persist_path).parent.mkdir(parents=True, exist_ok=True)
    with open(persist_path, "w") as f:
        f.write(get_repo_global_understand(repo_path))
