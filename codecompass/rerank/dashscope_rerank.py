import dashscope
import logging

from http import HTTPStatus

# 配置日志
logger = logging.getLogger(__name__)


# 如果没有配置根日志器，则进行基本配置
if not logging.getLogger().handlers:
    logging.basicConfig(
        level=logging.INFO,
        format="%(asctime)s - %(filename)s:%(lineno)s - %(levelname)s - %(message)s",
        datefmt="%Y-%m-%d %H:%M:%S",
    )


def rerank(
    query,
    documents: list,
    model_name: str = None,
    top_n=10,
    threshold=0.1,
):
    """
    重排序函数，使用配置的rerank模型
    """
    # 获取模型配置
    if model_name is None:
        import sys
        from pathlib import Path
        sys.path.append(str(Path(__file__).parent.parent))
        from model_factory import get_model_factory

        model_factory = get_model_factory()
        rerank_config = model_factory.get_rerank_config()
        model_name = rerank_config.model_name

        # 如果配置了API密钥，设置环境变量
        if rerank_config.api_key:
            import os
            os.environ["DASHSCOPE_API_KEY"] = rerank_config.api_key

    resp = dashscope.TextReRank.call(
        model=model_name,
        query=query,
        documents=documents,
        # top_n=10,
        top_n=top_n,
        return_documents=True,
    )
    if resp.status_code == HTTPStatus.OK:
        results = resp["output"]["results"]
        results = [
            (item["index"], item["document"]["text"])
            for item in results
            if item["relevance_score"] > threshold
        ]
        return results


if __name__ == "__main__":
    print(
        rerank(
            "如何在python中使用多线程",
            ["Rust 多线程", "Python 多进程", "Python 协程", "python 多线程"],
        )
    )
